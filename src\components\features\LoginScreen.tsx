
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { signInWithGoogle } from '@/services/api';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/common/Button';
import { LeafIcon } from '@/components/common/icons';
import { logger } from '@/utils/logger';
import { useResponsiveBackground } from '@/hooks/useResponsiveBackground';
import { getBackgroundConfig } from '@/utils/backgroundImages';

const LoginScreen: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { user, loading } = useAuth();

  // Configuration de l'image de fond responsive
  const backgroundConfig = getBackgroundConfig('login');
  const { backgroundStyles, isReady } = useResponsiveBackground(backgroundConfig);

  // Redirection automatique si l'utilisateur est déjà connecté
  useEffect(() => {
    if (!loading && user) {
      logger.info('Redirection automatique - utilisateur déjà connecté', {
        component: 'LoginScreen',
        action: 'AutoRedirect'
      });
      navigate('/', { replace: true });
    }
  }, [user, loading, navigate]);

  const handleGoogleSignIn = async () => {
    logger.debug('Clic sur le bouton de connexion Google', {
      component: 'LoginScreen',
      action: 'LoginButtonClick'
    });

    setIsLoading(true);
    setError(null);

    try {
      const result = await signInWithGoogle();

      if (result?.success) {
        logger.info('Connexion réussie, attente de la mise à jour du contexte', {
          component: 'LoginScreen',
          action: 'LoginSuccess'
        });

        // 🎯 CORRECTION CRITIQUE : Plus de setTimeout hack !
        // La redirection sera gérée automatiquement par l'useEffect
        // quand l'AuthContext se mettra à jour via onAuthStateChanged
        // Ceci élimine les race conditions et rend le système robuste

      } else if (result?.error) {
        logger.warn('Erreur de connexion', {
          component: 'LoginScreen',
          action: 'LoginError'
        }, { error: result.error });
        setError(result.error);
      }
    } catch (err) {
      logger.error('Erreur inattendue lors de la connexion', {
        component: 'LoginScreen',
        action: 'LoginUnexpectedError'
      }, err);
      setError("Une erreur inattendue s'est produite. Veuillez réessayer.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="min-h-screen flex items-center justify-center p-4 relative"
      style={backgroundStyles}
    >
      {/* Overlay pour améliorer la lisibilité */}
      <div className="absolute inset-0 bg-black/40 z-10"></div>

      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md text-center relative z-20"
      >
        <div className="inline-block p-4 bg-gradient-to-r from-[#d385f5] to-[#a364f7] rounded-full mb-6">
          <img
            src={getBackgroundConfig('logo').url}
            alt="FloraSynth Logo"
            className="w-16 h-16 object-contain"
            onError={(e) => {
              // Fallback vers l'icône LeafIcon si l'image ne charge pas
              const target = e.target as HTMLImageElement;
              target.style.display = 'none';
              target.nextElementSibling?.classList.remove('hidden');
            }}
          />
          <LeafIcon className="w-16 h-16 text-white hidden" />
        </div>
        <h1 className="text-5xl font-bold text-white mb-2 drop-shadow-lg">FloraSynth</h1>
        <p className="text-lg text-[#E0E0E0] mb-8 drop-shadow-md">Votre Assistant IA Personnel pour le Soin des Plantes</p>

        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-300 text-sm"
          >
            {error}
          </motion.div>
        )}

        <Button
          onClick={handleGoogleSignIn}
          className="w-full max-w-xs mx-auto"
          disabled={isLoading}
        >
          {isLoading ? 'Connexion en cours...' : 'Se connecter avec Google'}
        </Button>
      </motion.div>
    </div>
  );
};

export default LoginScreen;
